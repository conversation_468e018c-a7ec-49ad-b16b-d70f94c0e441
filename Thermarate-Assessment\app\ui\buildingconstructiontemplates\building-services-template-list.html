<section id="buildingservicestemplate-list-view" class="main-content-wrapper" data-ng-controller="BuildingServicesTemplateListCtrl as vm">

    <div class="widget">
        <div layout="row" style="padding: 10px 0px;">
            <h1 style="margin: auto 0px; font-size: 20px; line-height: 28px; font-weight: 400;">
                {{vm.title}}
            </h1>
        </div>

        <div class="filters-container">
            <!-- Search -->
            <div class="filter">
                <div class="filter-label">Search</div>
                <div class="search-input-container">
                    <input class="search-input"
                           type="text"
                           placeholder="Quick Filter"
                           ng-model="vm.searchString"
                           ng-keydown="$event.keyCode == 13 ? vm.refreshList() : null"
                           ng-blur="vm.refreshList()">
                    <img src="/content/images/cross.png"
                         class="search-clear-button"
                         ng-show="vm.searchString"
                         ng-click="vm.searchString = null; vm.refreshList(); $event.stopPropagation()">
                </div>
            </div>

            <!-- Multi-Filters -->
            <div ng-repeat="filter in vm.filters track by filter.field"
                 ng-if="filter.name != null && vm.initialiseComplete"
                 ng-show="filter.section == 1 || vm.filtersExpanded"
                 class="filter {{vm.anyOptionsSelectedOnField(filter, vm.appliedFilters) ? 'options-selected' : ''}}">

                <!-- Label -->
                <div ng-if="filter.name != '[blank]' && filter.name != '[moreLessButton]'" class="filter-label">{{vm.keyToName(filter.name)}}</div>

                <!-- Clear Button -->
                <img src="/content/images/cross.png"
                     class="filter-clear-button"
                     ng-click="vm.clearFilter(filter);$event.stopPropagation()"
                     ng-show="vm.anyOptionsSelectedOnField(filter, vm.appliedFilters)"
                />

                <!-- Filter Options -->
                <md-select ng-model="vm.appliedFilters[filter.field]"
                          multiple
                          placeholder="All"
                          ng-change="vm.refreshList()"
                          style="min-width: 150px;">
                    <md-option ng-repeat="option in vm.filterOptions[filter.field] track by option.value"
                              ng-value="option.value">
                        {{option.name}} <span ng-if="vm.filterCountData[filter.field] && vm.filterCountData[filter.field][option.value] != null">({{vm.filterCountData[filter.field][option.value]}})</span>
                    </md-option>
                </md-select>
            </div>
        </div>

        <!-- Number of Items -->
        <div class="current-filter-description {{vm.filtersExpanded ? null : 'add-margin'}}" ng-show="!vm.filtersApplied">
            Showing {{vm.totalRecords}} templates
        </div>
        <div class="current-filter-description {{vm.filtersExpanded ? null : 'add-margin'}}" ng-show="vm.filtersApplied">
            {{vm.totalFilteredRecords}} of {{vm.totalRecords}} templates match your filters
            (<u ng-click="vm.clearFilters()" style="text-decoration: underline; cursor:pointer;">clear filters</u>)
        </div>
        <div class="table-responsive-vertical shadow-z-1">
            <table class="table table-striped table-hover table-condensed"
                    st-table="vm.servicesTemplateList"
                    st-table-filtered-list="exportList"
                    st-global-search="vm.listFilter"
                    st-persist="ServicesTemplate"
                    st-pipe="vm.callServer"
                    st-sticky-header>
                <thead>
                    <tr>
                        <th st-sort="templateName" class="can-sort text-left">Template Name</th>
                        <th st-sort="notes" class="can-sort text-left">Notes</th>
                        <th style="width: 10%;"></th>
                    </tr>

                </thead>

                <tbody>
                    <tr ng-repeat="row in vm.servicesTemplateList" class="list-row clickable">
                        <td data-title="Name" ng-click="vm.goToItem(row.buildingServicesTemplateId)">
                            <div style="width: 100%; padding-left: 10px; padding-right: 40px; box-sizing: border-box; text-align: left;">
                                {{row.templateName}}
                                <div class="go-to-variation-button" style="order:3;"> <img src="/content/images/arrow-right.png" /> </div>
                            </div>
                        </td>
                        <td data-title="Notes" class="text-left" ng-click="vm.goToItem(row.buildingServicesTemplateId)">{{::row.notes}}</td>
                        <td data-title="Notes" class="text-center" >
                            <div layout="row"
                                 layout-align="center center">
                                <md-button class="md-raised md-icon-button"
                                           redi-enable-roles="admin__template__delete"
                                           title="Delete Template"
                                           ng-click="vm.delete(row)">
                                    <i class="fa fa-eraser fa-lg"></i>
                                </md-button>

                                <md-button ng-if="item.isLocked!=true"
                                           redi-enable-roles="admin__template__create"
                                           style="margin-right: 5px;"
                                           class="md-raised md-icon-button"
                                           title="Copy Template"
                                           ng-click="vm.clone(row)">
                                        <i class="fa fa-clone fa-lg"></i>
                                </md-button>
                            </div>
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="5" class="text-center">
                            <div st-pagination="" st-items-by-page="100" st-displayed-pages="10"></div>
                        </td>
                    </tr>
                </tfoot>
            </table>
            <div class="widget-pager">
                <span>Showing {{vm.showingFromCnt}} - {{vm.showingToCnt}} of {{vm.totalRecords}}</span>
            </div>
        </div>
        <div class="widget-foot">
            <div class="clearfix"></div>
        </div>
    </div>
</section>

<style>

    .list-row {
        height: 52px;
    }

    .list-row:hover .go-to-variation-button {
        visibility: visible;
    }

    .go-to-variation-button {
        visibility: hidden;
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 7%;
        width: 25px;
        height: 25px;
        min-width: 25px;
        min-height: 25px;
        border-radius: 4px;
        cursor: pointer;
    }

        .go-to-variation-button:hover {
            background-color: #d1d1d1;
        }

        .go-to-variation-button > img {
            position: absolute;
            top: 50%;
            left: 54%;
            transform: translate(-50%, -50%);
            width: 60%;
            height: auto;
        }

</style>